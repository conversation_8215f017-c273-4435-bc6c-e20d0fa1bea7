"use client";

import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { But<PERSON> } from "@/components/ui/button";
import { addDoc, collection } from "firebase/firestore";
import { db, auth } from "@/lib/firebaseConfig";
import { useToast } from "@/components/ui/toast/use-toast";

import {
	Form,
	FormControl,
	FormField,
	FormItem,
	FormMessage,
} from "@/components/ui/form";

const contactFormSchema = z.object({
	name: z
		.string()
		.min(1, { message: "Veuillez saisir votre nom" })
		.max(100, { message: "Veuillez indiquer un nom plus court" }),
	email: z
		.string()
		.email({ message: "Veuillez saisir une adresse email valide" }),
	phone: z.string().regex(/^\+?\d{10,15}$/, {
		message: "Veuillez saisir un numéro de téléphone valide",
	}),
	comment: z
		.string()
		.min(10, { message: "Le commentaire doit contenir au moins 10 caractères" })
		.max(1000, {
			message: "Le commentaire ne doit pas dépasser 1000 caractères",
		}),
});

type EmailData = {
	to: string;
	message: {
		subject: string;
		html: string;
	};
	user?: string;
};

export default function ContactForm() {
	const { toast } = useToast();

	const form = useForm<z.infer<typeof contactFormSchema>>({
		resolver: zodResolver(contactFormSchema),
		defaultValues: {
			name: "",
			email: "",
			phone: "",
			comment: "",
		},
	});

	const onSubmit = async (values: z.infer<typeof contactFormSchema>) => {
		try {
			// Get current user synchronously
			const currentUser = auth.currentUser;

			// Prepare email data
			const emailData: EmailData = {
				to: "<EMAIL>",
				message: {
					subject: `Un message de ${values.name} (allocvc.fr)`,
					html: `
				<html>
				  <body>
				  <p>Bonjour,</p>
				  <p>Nous avons reçu un nouveau message via le formulaire de contact du site alloCVC.</p>
				  <p>Voici les détails du message :</p>
				  <ul>
					<li>Nom : ${values.name}</li>
					<li>Adresse email : ${values.email}</li>
					<li>Téléphone : ${values.phone}</li>
					<li>Message : ${values.comment}</li>
				  </ul>
				  <p>Merci de prendre contact avec ce client pour traiter sa demande.</p>
				  <p>Cordialement,</p>
				  <p>L'équipe alloCVC</p>
				  </body>
				</html>
			  `,
				},
			};

			// Only add user field if user is authenticated
			if (currentUser) {
				emailData.user = currentUser.uid;
			}

			// Add document to Firestore
			await addDoc(collection(db, "mail"), emailData);

			// Show success message
			toast({
				title: "Message envoyé avec succès",
				description:
					"Votre message a été envoyé avec succès. Nous vous recontacterons dès que possible. Merci de nous avoir contacté !",
				variant: "default",
			});

			// Reset form
			form.reset();
		} catch (error) {
			console.error(error);
			toast({
				title: "Erreur lors de l'envoi du message",
				description:
					"Désolé, une erreur est survenue lors de l'envoi de votre message. Veuillez réessayer plus tard ou nous contacter directement par téléphone ou par courriel.",
				variant: "destructive",
			});
		}
	};

	return (
		<section
			id="contactFormSection"
			className="border-allo-line box-border flex h-auto w-full items-center border-b py-10 lg:items-stretch lg:pb-20"
		>
			<div className="bg-allo-bg-2 flex min-h-auto w-full flex-col items-center justify-center gap-10 rounded-xl px-10 py-10 md:py-20 lg:flex-row lg:items-start lg:gap-10 xl:pt-14 xl:pb-20">
				<div className="box-border flex h-auto flex-1 flex-col items-center lg:items-start lg:justify-start lg:pl-20 xl:py-20 xl:pr-10 xl:pl-40">
					<div className="flex w-full flex-col items-center text-center lg:items-start lg:text-left">
						<h2 className="bg-allo-grad font-primary text-t-15 inline-block bg-clip-text font-semibold text-transparent uppercase">
							Contactez-Nous
						</h2>
						<h3 className="font-primary text-allo-dark-grey mt-2 mb-6 text-2xl font-bold lg:text-3xl">
							Nous sommes à votre disposition
						</h3>
						<p className="font-primary text-allo-dark-grey text-sm font-normal lg:text-base">
							109 avenue Maréchal Lyautey
							<br />
							06300 NICE
						</p>
						<p className="font-primary text-allo-dark-grey mt-5 text-sm font-normal lg:text-base">
							<span className="font-semibold">Appelez-nous</span>: +33 (0) 4 93
							07 92 02
						</p>
					</div>
				</div>
				<div className="box-border flex h-auto flex-1 flex-col justify-end lg:pr-20 xl:py-20 xl:pr-40 xl:pl-10">
					<Form {...form}>
						<form
							onSubmit={form.handleSubmit(onSubmit)}
							className="w-full space-y-4"
						>
							<div className="flex gap-3 max-md:flex-wrap">
								<div className="basis-1/2 max-md:basis-full">
									{/* Name */}
									<FormField
										control={form.control}
										name="name"
										render={({ field }) => (
											<FormItem>
												{/* <FormLabel>Nom</FormLabel> */}
												<FormControl>
													<Input
														autoComplete="given-name"
														className="bg-white text-sm"
														placeholder="Nom"
														{...field}
													/>
												</FormControl>
												<FormMessage />
											</FormItem>
										)}
									/>
								</div>

								<div className="basis-1/2 max-md:basis-full">
									{/* Phone */}
									<FormField
										control={form.control}
										name="phone"
										render={({ field }) => (
											<FormItem>
												{/* <FormLabel>Téléphone</FormLabel> */}
												<FormControl>
													<Input
														autoComplete="tel"
														className="bg-white text-sm"
														placeholder="Téléphone"
														{...field}
													/>
												</FormControl>
												<FormMessage />
											</FormItem>
										)}
									/>
								</div>
							</div>
							<div className="flex">
								<div className="w-full">
									{/* Email */}
									<FormField
										control={form.control}
										name="email"
										render={({ field }) => (
											<FormItem>
												{/* <FormLabel>Email</FormLabel> */}
												<FormControl>
													<Input
														autoComplete="email"
														className="bg-white text-sm"
														placeholder="Email"
														{...field}
													/>
												</FormControl>
												<FormMessage />
											</FormItem>
										)}
									/>
								</div>
							</div>

							<div className="flex w-full">
								<div className="w-full">
									{/* Comment */}
									<FormField
										control={form.control}
										name="comment"
										render={({ field }) => (
											<FormItem>
												<FormControl>
													<Textarea
														className="bg-white text-sm"
														placeholder="Votre commentaire"
														{...field}
													/>
												</FormControl>
												<FormMessage />
											</FormItem>
										)}
									/>
								</div>
							</div>

							<Button variant="primary" type="submit">
								Envoyer
							</Button>
						</form>
					</Form>
				</div>
			</div>
		</section>
	);
}
