import { useEffect, useState, useMemo } from "react";
import { Button } from "@/components/ui/button";
import { db } from "@/lib/firebaseConfig";
import { collection, query, where, getDocs } from "firebase/firestore";
import { getAffectedTimeSlots } from "./Functions";
import { interventionsWouldOverlap } from "./Functions";
import { ChevronLeft } from "lucide-react";
import { DateTime } from "luxon";

export default function Horaires({
	handeTimeSelection,
	selectedDate,
	interventionTotalDuration,
	setIsVisible,
}: {
	handeTimeSelection: (time: string) => void;
	selectedDate: Date | undefined;
	interventionTotalDuration: number;
	setIsVisible: (visible: boolean) => void;
}) {
	const [buttonSize, setButtonSize] = useState<"sm" | "lg" | "default">("lg");
	const [unavailableTimes, setUnavailableTimes] = useState<Set<string>>(
		new Set(),
	);
	const morningTimes = useMemo(
		() => ["09:00", "09:30", "10:00", "10:30", "11:00", "11:30"],
		[],
	);
	const eveningTimes = useMemo(
		() => [
			"14:00",
			"14:30",
			"15:00",
			"15:30",
			"16:00",
			"16:30",
			"17:00",
			"17:30",
			"18:00",
		],
		[],
	);
	const allTimeSlots = useMemo(
		() => [...morningTimes, ...eveningTimes],
		[morningTimes, eveningTimes],
	);

	useEffect(() => {
		const updateSize = () => {
			const ScreenSize = window.innerWidth;
			if (ScreenSize < 640) setButtonSize("sm");
			else if (ScreenSize < 1024) setButtonSize("sm");
			else setButtonSize("lg");
		};
		updateSize();
		window.addEventListener("resize", updateSize);
		return () => window.removeEventListener("resize", updateSize);
	}, []);

	useEffect(() => {
		const fetchUnavailableTimes = async () => {
			if (!selectedDate) return;

			const dateString = selectedDate.toLocaleDateString("fr-CA");

			try {
				// Query all interventions for the selected date
				const interventionsQuery = query(
					collection(db, "interventions"),
					where("interventionDate", "==", dateString),
					where("paymentStatus", "==", "complete"),
				);

				const snapshot = await getDocs(interventionsQuery);
				const unavailableTimeSlots = new Set<string>();
				const existingInterventions: { startTime: string; duration: number }[] =
					[];

				snapshot.docs.forEach((doc) => {
					const data = doc.data();
					if (data.interventionTime && data.interventionTotalDuration) {
						existingInterventions.push({
							startTime: data.interventionTime,
							duration: data.interventionTotalDuration,
						});

						const affectedSlots = getAffectedTimeSlots(
							data.interventionTime,
							data.interventionTotalDuration,
						);
						affectedSlots.forEach((slot) => unavailableTimeSlots.add(slot));
					}
				});

				allTimeSlots.forEach((timeSlot) => {
					// Skip if already marked as unavailable
					if (unavailableTimeSlots.has(timeSlot)) return;

					const wouldOverlap = existingInterventions.some((existing) => {
						return interventionsWouldOverlap(
							timeSlot,
							interventionTotalDuration,
							existing.startTime,
							existing.duration,
						);
					});

					if (wouldOverlap) {
						unavailableTimeSlots.add(timeSlot);
					}
				});

				//  RÈGLES SPÉCIFIQUES : 11/08 (lun matin), 12/08 (mar matin), 14/08 (jeu matin)
				const key = DateTime.fromJSDate(selectedDate, {
					zone: "Europe/Paris",
				}).toFormat("yyyy-MM-dd");

				if (key === "2025-08-11" || key === "2025-08-14") {
					// Lundi 11/08 et Jeudi 14/08 → matin indisponible
					morningTimes.forEach((t) => unavailableTimeSlots.add(t));
				}

				if (key === "2025-08-12") {
					// Mardi 12/08 → matin indisponible
					morningTimes.forEach((t) => unavailableTimeSlots.add(t));
				}
				// Fin des règles spécifiques
				setUnavailableTimes(unavailableTimeSlots);
			} catch (error) {
				console.warn("Error fetching interventions:", error);
			}
		};

		fetchUnavailableTimes();
	}, [selectedDate, allTimeSlots, interventionTotalDuration, morningTimes]);

	const renderTimeGrid = (times: string[], title: string) => (
		<div className="w-full space-y-3">
			<h4 className="text-center">{title}</h4>
			<div className="grid w-full grid-cols-2 gap-4 sm:grid-cols-3 md:gap-6 2xl:grid-cols-4">
				{times.map((time) => {
					const isUnavailable = unavailableTimes.has(time);
					return (
						<Button
							key={time}
							size={buttonSize}
							variant={isUnavailable ? "outline" : "default2"}
							onClick={() => handeTimeSelection(time)}
							disabled={isUnavailable}
							className={`${
								isUnavailable ? "cursor-not-allowed text-gray-400" : ""
							}`}
						>
							{time}
						</Button>
					);
				})}
			</div>
		</div>
	);

	return (
		<>
			<div className="absolute top-4 left-4 flex items-center gap-2">
				<ChevronLeft
					className="text-allo-md-grey size-6 cursor-pointer"
					onClick={() => setIsVisible(true)}
				/>
				<p
					className="text-allo-md-grey cursor-pointer text-xs"
					onClick={() => setIsVisible(true)}
				>
					Calendrier
				</p>
			</div>
			<div className="flex w-full flex-col items-center gap-6 px-2 lg:gap-8">
				{renderTimeGrid(morningTimes, "Matin")}
				{renderTimeGrid(eveningTimes, "Soir")}
			</div>
		</>
	);
}
