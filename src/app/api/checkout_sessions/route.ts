import { NextResponse } from "next/server";
import { interventionDataSchema } from "@/lib/stripeProductSchema";
import { StripeError } from "@stripe/stripe-js";
import { URL } from "url";
import Stripe from "stripe";

const stripe = new Stripe(
	process.env.NODE_ENV === "production"
		? process.env.NEXT_PUBLIC_STRIPE_SECRET_KEY!
		: process.env.NEXT_PUBLIC_STRIPE_SECRET_TEST_KEY!,
	{
		apiVersion: "2025-02-24.acacia",
	},
);

const taxRateId =
	process.env.NODE_ENV === "production"
		? (process.env.STRIPE_LIVE_TAX_RATE_ID as string)
		: (process.env.STRIPE_TEST_TAX_RATE_ID as string);

export async function POST(req: Request) {
	if (req.method !== "POST") {
		return NextResponse.json(
			{ error: { message: "Method Not Allowed" } },
			{ status: 405 },
		);
	}

	try {
		const body = await req.json();
		const parsedData = interventionDataSchema.safeParse(body.data);
		if (!parsedData.success) {
			return NextResponse.json(
				{ error: { message: "Invalid data" } },
				{ status: 400 },
			);
		}
		const data = parsedData.data;

		const interventionDate = data.interventionDate; // Format  "yyyy-mm-dd" pour le calendrier.

		console.log("Server CS: ", data);
		const product = await stripe.products.create({
			name: `Intervention - ${interventionDate} à ${data.interventionTime} - ${data.commune}.`,
			description: `Service ${data.maintenance ? "d'entretien." : "de recherche de panne."} Type d'équipment : ${data.chaudiere ? "Chaudière" : data.clim ? "Climatiseur" : data.pompe ? "Pompe à chaleur" : ""}`,
			shippable: true, // (Pour que stripe  détecte l'adresse de livraison)

			metadata: {
				chaudiere: data.chaudiere,
				clim: data.clim,
				climSubOptions: JSON.stringify(data.climSubOptions), // Convert complex objects to strings
				commune: data.commune,
				communeDept: data.communeDept,
				communeZone: data.communeZone,
				interventionDate: data.interventionDate,
				interventionTime: data.interventionTime,
				interventionTotalPrice: data.interventionTotalPrice,
				interventionGrossPrice: data.interventionGrossPrice,
				interventionTravelPrice: data.interventionTravelPrice,
				maintenance: data.maintenance,
				panne: data.panne,
				pompe: data.pompe,
				userId: data.userId,
				userEmail: data.userEmail,
				interventionId: data.interventionId,
			},
		});

		const price = await stripe.prices.create({
			unit_amount: data.interventionTotalPrice * 100, // Convert to cents
			currency: "eur",
			product: product.id,
		});
		const customer = await stripe.customers.create({
			email: data.userEmail,
		});

		// Create a PaymentIntent with the order amount and currency
		const paymentIntent = await stripe.checkout.sessions.create({
			//customer_creation: "always",
			ui_mode: "embedded",
			customer: customer.id,
			submit_type: "pay",
			billing_address_collection: "required",
			phone_number_collection: {
				enabled: true,
			},

			line_items: [
				{
					price: price.id,
					quantity: 1,
					tax_rates: [taxRateId],
				},
			],
			mode: "payment",
			return_url: `${req.headers.get("origin")}/#reservationSection?session_id={CHECKOUT_SESSION_ID}`,
			allow_promotion_codes: true,
			metadata: {
				interventionId: data.interventionId,
				userId: data.userId,
				interventionDate: data.interventionDate,
				interventionTime: data.interventionTime,
			},
			payment_intent_data: {
				metadata: {
					userEmail: data.userEmail,
					interventionDate: interventionDate,
					interventionTime: data.interventionTime,
					commune: data.commune,
					totalPrice: data.interventionTotalPrice,
					grossPrice: data.interventionGrossPrice,
					priceId: price.id,
					maintenance: data.maintenance.toString(),
					panne: data.panne.toString(),
					chaudiere: data.chaudiere.toString(),
					clim: data.clim.toString(),
					pompe: data.pompe.toString(),
				},
			},
		});

		return NextResponse.json({ clientSecret: paymentIntent.client_secret });
	} catch (error) {
		console.log("Server CS error: ", error);
		const stripeError = error as StripeError;
		return NextResponse.json(
			{
				error: {
					message: stripeError.message,
					type: stripeError.type,
					code: stripeError.code,
				},
			},
			{ status: Number(stripeError.code) || 500 },
		);
	}
}

export async function GET(req: Request) {
	try {
		const url = new URL(req.url);
		const sessionId = url.searchParams.get("session_id");
		if (!sessionId) {
			return NextResponse.json(
				{ error: { message: "Session ID is missing" } },
				{ status: 400 },
			);
		}

		const session = await stripe.checkout.sessions.retrieve(sessionId, {
			expand: [
				"customer",
				"shipping",
				"shipping_address_collection",
				"shipping_options",
			],
		});

		return NextResponse.json({
			status: session.status,
			customer_email: session.customer_details?.email,
			customer: session.customer,
		});
	} catch (error) {
		const stripeError = error as StripeError;
		return NextResponse.json(
			{
				error: {
					message: stripeError.message,
					type: stripeError.type,
					code: stripeError.code,
				},
			},
			{ status: 500 },
		);
	}
}
