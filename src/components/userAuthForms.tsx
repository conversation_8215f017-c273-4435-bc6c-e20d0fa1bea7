"use client";

import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import PwdInput from "@/app/User/pwdInput";
import { useState } from "react";
import GeneralConditionsDialog from "@/app/TermsAndConditions/ConditionsGenerales";
import PolicyDialog from "@/app/TermsAndConditions/PolitiqueDeConfidentialite";

type AuthFormsProps = {
	intitialExistingUser: boolean;
	password: string;
	setPassword: (password: string) => void;
	signUpAction:
		| string
		| ((formData: FormData) => void | Promise<void>)
		| undefined;
	logInAction:
		| string
		| ((formData: FormData) => void | Promise<void>)
		| undefined;
	signUpData:
		| {
				email: string;
				displayName?: string | undefined;
		  }
		| undefined;
	logInData: { email: string } | undefined;
	signUpPending: boolean;
	logInPending: boolean;
};

export function AuthForms({
	intitialExistingUser,
	password,
	setPassword,
	signUpAction,
	logInAction,
	signUpData,
	logInData,
	signUpPending,
	logInPending,
}: AuthFormsProps) {
	const [existingUser, setExistingUser] =
		useState<boolean>(intitialExistingUser);
	const [isPasswordValid, setIsPasswordValid] = useState<boolean>(false);

	const handlePasswordValidityChange = (isValid: boolean) => {
		setIsPasswordValid(isValid);
	};

	return (
		<>
			<div className="flex flex-col space-y-1.5 text-center sm:text-center">
				<h4 className="text-lg leading-none font-semibold tracking-tight">
					{existingUser ? "Se connecter" : "S'inscrire"}
				</h4>
				<div className="text-sm text-neutral-500 dark:text-neutral-400">
					Veuillez saisir vos coordonnées.
				</div>
			</div>
			{!existingUser && (
				<form action={signUpAction} className="grid w-full gap-4 py-4">
					<div className="grid gap-4 py-4">
						<div className="grid min-w-2/3 grid-cols-4 items-center gap-4">
							<Label htmlFor="name" className="hidden text-right md:block">
								Nom
							</Label>
							<Input
								id="name"
								name="displayName"
								type="name"
								autoComplete="name"
								className="col-span-4 text-sm md:col-span-3 md:placeholder-transparent"
								placeholder="Nom"
								defaultValue={signUpData?.displayName}
							/>
							<Label htmlFor="email" className="hidden text-right md:block">
								Email
							</Label>
							<Input
								id="email"
								name="email"
								type="email"
								autoComplete="email"
								className="col-span-4 text-sm md:col-span-3 md:placeholder-transparent"
								placeholder="Email"
								defaultValue={signUpData?.email}
								required
							/>
						</div>
						<PwdInput
							password={password}
							onChange={(e) => setPassword(e.target.value)}
							onValidityChange={handlePasswordValidityChange}
							showRequirements={false}
						/>
					</div>

					<div className="mt-6 flex w-full items-center justify-center gap-3">
						<p className="text-xs">
							En vous inscrivant, vous acceptez nos{" "}
							<GeneralConditionsDialog>
								<span className="text-allo-pink cursor-pointer">
									Conditions Générales d&apos;Utilisation
								</span>
							</GeneralConditionsDialog>{" "}
							et notre{" "}
							<PolicyDialog>
								<span className="text-allo-pink cursor-pointer">
									Politique de Confidentialité
								</span>
							</PolicyDialog>
						</p>
					</div>

					<div className="flex justify-center">
						<Button
							className="w-full"
							type="submit"
							size={"lg"}
							disabled={!isPasswordValid || signUpPending}
						>
							S&apos;inscrire
						</Button>
					</div>
				</form>
			)}
			{existingUser && (
				<form action={logInAction} className="grid gap-4 py-4">
					<div className="grid gap-4 py-4">
						<div className="grid grid-cols-4 items-center gap-4">
							<Label htmlFor="email" className="hidden text-right md:block">
								Email
							</Label>
							<Input
								id="email"
								name="email"
								type="email"
								autoComplete="email"
								placeholder="Email"
								className="col-span-4 text-sm md:col-span-3 md:placeholder-transparent"
								defaultValue={logInData?.email}
								required
							/>
						</div>
						<PwdInput
							password={password}
							onChange={(e) => setPassword(e.target.value)}
							onValidityChange={handlePasswordValidityChange}
							showRequirements={true}
						/>
					</div>
					<div className="flex justify-center">
						<Button
							className="w-full"
							type="submit"
							size={"lg"}
							disabled={!isPasswordValid || logInPending}
						>
							Se connecter
						</Button>
					</div>
				</form>
			)}
			<div className="mt-4 flex flex-col justify-center gap-0.5 text-center text-sm md:flex-row">
				{existingUser ? (
					<span>Vous n&apos;avez pas de compte ?</span>
				) : (
					<span>Vous avez déjà un compte ?</span>
				)}
				<div
					className="text-allo-pink ml-1 cursor-pointer font-semibold"
					onClick={() => {
						setExistingUser(!existingUser);
						setPassword("");
					}}
				>
					{existingUser ? "Créez un compte" : "Connectez-vous"}
				</div>
			</div>
		</>
	);
}
